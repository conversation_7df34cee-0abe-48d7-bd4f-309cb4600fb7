/*ITIS 3135 Assignment2 css file*/

/*styling for the table goes here*/
body
{
	width: 750px;
	margin: 0 auto;
}

/* Figure and caption styling */
figure
{
	margin: 20px;
}

figcaption
{
	font-weight: bold;
	font-size: 1.2em;
	text-align: center;
	margin-bottom: 10px;
	color: #333;
}

table
{
	border-collapse: collapse;
	border: 1px solid black;
	width: 100%;
}

th, td
{
	border: 1px solid black;
	padding: .2em 1em .2em .5em;
	text-align: left;
	vertical-align: middle;
}

.right
{
	text-align: right;
}

/* Pseudo-class styling for alternating row colors */
tbody tr:nth-child(odd)
{
	background-color: #f9f9f9;
}

tbody tr:nth-child(even)
{
	background-color: #ffffff;
}

/* Header styling */
thead th
{
	background-color: #e6e6e6;
	font-weight: bold;
}

/* Highlight styling for personal row */
.highlight
{
	background-color: #ffff99 !important;
	font-weight: bold;
}

/* Total row styling */
.total
{
	background-color: #d4edda !important;
	font-weight: bold;
	border-top: 2px solid #333;
}


